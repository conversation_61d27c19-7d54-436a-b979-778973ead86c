<route lang="jsonc" type="page">
{
  "layout": "default",
  "style": {
    "navigationBarTitleText": ""
  }
}
</route>

<script lang="ts" setup>
//
</script>

<template>
  <view class="header">
    <view class="header-content">
      <view class="header-left">
        <image src="@/static/logo.jpg" class="logo" mode="aspectFit" />
        <view class="header-text">
          <text class="title">
            Mon电竞
          </text>
          <text class="subtitle">
            礼拜一电竞
          </text>
        </view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
//
</style>
